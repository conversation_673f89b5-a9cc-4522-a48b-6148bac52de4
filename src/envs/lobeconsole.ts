import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const getLobeConsoleEnv = () => {
  return createEnv({
    client: {
      NEXT_PUBLIC_LOBE_CONSOLE_URL: z.string(),
      NEXT_PUBLIC_LOBE_LICENSE: z.string(),
    },
    runtimeEnv: {
      LOBE_CONSOLE_URL: process.env.NEXT_PUBLIC_LOBE_CONSOLE_URL,
      LOBE_LICENSE: process.env.NEXT_PUBLIC_LOBE_LICENSE,
      NEXT_PUBLIC_LOBE_CONSOLE_URL: process.env.NEXT_PUBLIC_LOBE_CONSOLE_URL,
      NEXT_PUBLIC_LOBE_LICENSE: process.env.NEXT_PUBLIC_LOBE_LICENSE,
    },
    server: {
      LOBE_CONSOLE_URL: z.string(),
      LOBE_LICENSE: z.string(),
    },
  });
};

export const lobeConsoleEnv = getLobeConsoleEnv();
