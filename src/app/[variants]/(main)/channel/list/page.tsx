'use client';

import { Icon } from '@lobehub/ui';
import { <PERSON><PERSON>, Col, Row, message } from 'antd';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { trpcClient, trpcQuery } from '@/libs/trpc/client';
import { AiProviderWithModels } from '@/types/adminInfra';

import ChannelDetail from '../features/ChannelDetail';
import ChannelDrawer from '../features/ChannelDrawer';
import ChannelMenu from '../features/ChannelMenu';

const ChannelPage = () => {
  const [newOpen, setNewOpen] = useState<{ data?: AiProviderWithModels; open: boolean }>({
    open: false,
  });
  const { t } = useTranslation('channel');
  const [activeChannel, setActiveChannel] = useState<AiProviderWithModels>();
  const {
    data,
    isFetching: isLoading,
    refetch,
  } = trpcQuery.adminInfra.getAllInfraProviders.useQuery();

  // 新建按钮点击事件
  const handleNew = () => setNewOpen({ open: true });
  const handleEdit = () => {
    if (!activeChannel) return;
    setNewOpen({ data: activeChannel, open: true });
  };
  const handleDelete = async () => {
    if (!activeChannel?.id) {
      message.error(t('action.noSelect'));
      return;
    }
    try {
      await trpcClient.adminInfra.deleteInfraProvider.mutate(activeChannel.id);
      message.success(t('action.deleteSuccess'));

      // 刷新渠道列表数据
      refetch();

      // 删除当前选中的渠道，清空选中状态
      setActiveChannel(undefined);
    } catch (error: any) {
      message.error(`${t('action.deleteFailed')}: ${error.message}`);
    }
  };
  const handleToggle = async (enabled: boolean) => {
    try {
      await trpcClient.adminInfra.toggleInfraProviderStatus.mutate({
        enabled,
        providerId: activeChannel?.id || '',
      });
      message.success(t(`action.${enabled ? 'enableSuccess' : 'disableSuccess'}`));

      refetch();
    } catch (error: any) {
      message.error(
        `${t(`action.${enabled ? 'enableFailed' : 'disableFailed'}`)}: ${error.message}`,
      );
    }
  };

  useEffect(() => {
    // 同步当前所选渠道为最新的数据
    if (activeChannel && Array.isArray(data?.data)) {
      const find = data.data.find((item) => item.id === activeChannel.id);
      if (find) {
        setActiveChannel(find as AiProviderWithModels);
      }
    }
  }, [data]);
  return (
    <>
      <Row justify={'end'} style={{ marginTop: -60 }}>
        <Button onClick={handleNew} type={'primary'}>
          <Icon icon={Plus} size={{ size: 14 }} />
          {t('action.add')}
        </Button>
      </Row>
      <Row style={{ minHeight: 'calc(100vh -  242px)' }} wrap={false}>
        <Col flex="320px">
          <ChannelMenu
            activeChannel={activeChannel}
            data={(data?.data as AiProviderWithModels[]) || []}
            isLoading={isLoading}
            setActiveChannel={setActiveChannel}
          />
        </Col>
        <Col flex="auto">
          <ChannelDetail
            activeChannel={activeChannel}
            handleDelete={handleDelete}
            handleEdit={handleEdit}
            handleToggle={handleToggle}
          />
        </Col>
      </Row>
      <ChannelDrawer
        data={newOpen.data}
        onOk={() => {
          setNewOpen({ open: false });
          refetch();
        }}
        open={newOpen.open}
        setOpen={(e) => setNewOpen({ open: e })}
      />
    </>
  );
};

ChannelPage.displayName = 'ChannelPage';

export default ChannelPage;
