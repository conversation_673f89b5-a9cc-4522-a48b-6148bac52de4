'use client';

import { Tabs } from '@lobehub/ui';
import { useResponsive } from 'antd-style';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { Flexbox } from 'react-layout-kit';

import { FeatureFlag } from '@/const/featureFlag';
import { featureFlagSelectors } from '@/store/featureFlag/selectors';
import { useFeatureFlagStore } from '@/store/featureFlag/store';

export enum DashboardTab {
  Channel = 'channel',
  Overview = 'overview',
  Subscription = 'subscription',
  User = 'user',
}

interface NavProps {
  activeKey: DashboardTab;
  setActiveKey: (key: DashboardTab) => void;
}
const Nav = memo<NavProps>(({ activeKey, setActiveKey }) => {
  const { mobile } = useResponsive();
  const { t } = useTranslation('common');

  const [subscriptionEnabled] = useFeatureFlagStore((s) => [
    featureFlagSelectors.isEnabled(FeatureFlag.Subscription)(s),
  ]);

  return (
    <Flexbox
      style={{
        overflow: 'hidden',
        paddingInline: mobile ? 16 : 0,
        position: 'relative',
      }}
      width={'100%'}
    >
      <Tabs
        activeKey={activeKey}
        compact
        items={[
          { key: DashboardTab.Overview, label: t('overview.tabs.overview') },
          { key: DashboardTab.User, label: t('overview.tabs.user') },
          ...(subscriptionEnabled
            ? [
                {
                  key: DashboardTab.Subscription,
                  label: t('overview.tabs.subscription'),
                },
              ]
            : []),
          { key: DashboardTab.Channel, label: t('overview.tabs.channel') },
        ]}
        onChange={(key) => setActiveKey(key as DashboardTab)}
        style={{ marginBottom: 24 }}
      />
    </Flexbox>
  );
});

export default Nav;
