import { checkVercelEnv } from '@/envs/vercel';

import EnvError from './features/EnvError';
import SendTestMessage from './features/SendTestMessage';
import Settings from './features/Settings';
import { featureFlagSelectors, useFeatureFlagStore } from '@/store/featureFlag';
import { FeatureFlag } from '@/const/featureFlag';
import NotFound from '@/components/404';

const Page = () => {
  const isVercelEnvValid = checkVercelEnv();

  const [webhookEnabled] = useFeatureFlagStore((s) => [
    featureFlagSelectors.isEnabled(FeatureFlag.ConsoleWebhook)(s),
  ])

  if (!webhookEnabled) {
    return <NotFound />
  }

  if (!isVercelEnvValid) {
    return <EnvError />;
  }

  return (
    <>
      <SendTestMessage />
      <Settings />
    </>
  );
};

Page.displayName = 'ConsoleWebhook';

export default Page;
