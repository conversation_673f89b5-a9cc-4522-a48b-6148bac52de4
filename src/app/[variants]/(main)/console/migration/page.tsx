import NotFound from '@/components/404';
import { FeatureFlag } from '@/const/featureFlag';
import { featureFlagSelectors, useFeatureFlagStore } from '@/store/featureFlag';

import MigrationProviders from './features/MigrationProviders';

const Page = () => {
  const [migrationEnabled] = useFeatureFlagStore((s) => [
    featureFlagSelectors.isEnabled(FeatureFlag.ConsoleMigration)(s),
  ]);

  if (!migrationEnabled) {
    return <NotFound />;
  }

  return (
    <>
      {/*<TabsNav items={[{ key: 'providers', label: '服务商迁移' }]} />*/}
      <MigrationProviders />
    </>
  );
};

Page.displayName = 'ConsoleMigration';

export default Page;
